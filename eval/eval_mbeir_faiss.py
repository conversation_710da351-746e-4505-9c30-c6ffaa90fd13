import json
from transformers import AutoProcessor
import sys 
import os 
current_file_path = os.path.dirname(os.path.abspath(__file__))
module_path = os.path.join(current_file_path, "../")
sys.path.append(module_path)
from models.qwen2_vl import Qwen2VLRetForConditionalGeneration, AngleSimilarity
import torch 
import argparse
from dataset.datasets_mbeir import QueryDataset, CandidateDataset
from collators.mbeir_eval import MbeirQueryDataCollator, MbeirCandidateDataCollator
from torch.utils.data import DataLoader 
import torch.nn.functional as F 
from accelerate import Accelerator
import accelerate
from loaders.processor import LemuirProcessor
import numpy as np
import faiss
from tqdm import tqdm

DATASET_QUERY_NUM_UPPER_BOUND = 500000
DATASET_CAN_NUM_UPPER_BOUND = 10000000

def unhash_qid(qid):
    return qid

def unhash_did(did):
    return did
def load_qrel(filename):
    qrel = {}
    qid_to_taskid = {}
    with open(filename, "r") as f:
        for line in f:
            query_id, _, doc_id, relevance_score, task_id = line.strip().split()
            if int(relevance_score) > 0:  # Assuming only positive relevance scores indicate relevant documents
                if query_id not in qrel:
                    qrel[query_id] = []
                qrel[query_id].append(doc_id)
                if query_id not in qid_to_taskid:
                    qid_to_taskid[query_id] = task_id
    print(f"Retriever: Loaded {len(qrel)} queries from {filename}")
    print(
        f"Retriever: Average number of relevant documents per query: {sum(len(v) for v in qrel.values()) / len(qrel):.2f}"
    )
    return qrel, qid_to_taskid

def compute_recall_at_k(relevant_docs, retrieved_docs, k):
    retrieved_docs = retrieved_docs[:k]
    relevant_set = set(relevant_docs)
    retrieved_set = set(retrieved_docs)
    intersection = relevant_set.intersection(retrieved_set)
    if len(relevant_set) == 0:
        return 0.0
    return len(intersection) / len(relevant_set)

def tensors_to_device(batch, device):
    for key in batch:
        if isinstance(batch[key], torch.Tensor):
            batch[key] = batch[key].to(device)
    return batch

def eval_faiss(args):
    accelerator = Accelerator()
    device = accelerator.device
    is_main_process = accelerator.is_main_process
    
    # Load model
    model = Qwen2VLRetForConditionalGeneration.from_pretrained(
        args.model_id, 
        torch_dtype=torch.bfloat16,
        attn_implementation="flash_attention_2", 
        low_cpu_mem_usage=True, 
    )
    
    processor = LemuirProcessor.from_pretrained(args.original_model_id)
    tokenizer = processor.tokenizer
    tokenizer.padding_side = 'left'
    
    # Add embedding token
    emb_tokens = ["<emb>"]
    num_new_tokens = tokenizer.add_tokens(emb_tokens)
    emb_token_ids = tokenizer.convert_tokens_to_ids(emb_tokens)
    model.config.emb_token_ids = emb_token_ids
    if num_new_tokens > 0:
        model.resize_token_embeddings(len(tokenizer))
    
    model.eval()
    
    # Load datasets
    query_dataset = QueryDataset(
        query_data_path=args.query_data_path, 
        cand_pool_path=args.query_cand_pool_path,
        instructions_path=args.instructions_path,
        image_path_prefix=args.image_path_prefix
    )
    
    cand_dataset = CandidateDataset(
        query_data_path=args.query_data_path, 
        cand_pool_path=args.cand_pool_path,
        instructions_path=args.instructions_path,
        image_path_prefix=args.image_path_prefix
    )

    query_data_collator = MbeirQueryDataCollator(tokenizer=tokenizer, processor=processor)
    cand_data_collator = MbeirCandidateDataCollator(tokenizer=tokenizer, processor=processor)
    
    query_dataloader = DataLoader(query_dataset, batch_size=40, num_workers=4, shuffle=False, collate_fn=query_data_collator)
    candidate_dataloader = DataLoader(cand_dataset, batch_size=40, num_workers=4, shuffle=False, collate_fn=cand_data_collator)

    
    # Phase 1: Process candidate embeddings and build FAISS index on CPU
    print("Phase 1: Processing candidate embeddings...")
    candidate_features_list = []
    candidate_ids = []
    
    with torch.no_grad():
        candidate_dataloader, model = accelerator.prepare(candidate_dataloader, model)
        
        for batch in tqdm(candidate_dataloader, disable=not is_main_process, desc="Processing candidates"):
            batch = tensors_to_device(batch, device)
            candidate_embed, _, batch_candidate_ids = model(**batch, inference=True)
            candidate_embed = F.normalize(candidate_embed, dim=-1)
            candidate_embed = accelerator.gather_for_metrics(candidate_embed)
            batch_candidate_ids = accelerator.gather_for_metrics(batch_candidate_ids)[:len(candidate_embed)]
            candidate_ids.extend(batch_candidate_ids)
            # Move to CPU immediately to save GPU memory
            candidate_features_list.append(candidate_embed.cpu())
    
    # Build FAISS index on main process
    if is_main_process:
        print("Building FAISS index on CPU...")
        candidate_features = torch.cat(candidate_features_list, dim=0)
        candidate_features_np = candidate_features.float().numpy()
        
        dim = candidate_features_np.shape[1]
        if args.use_angle_sim:
            # For angle similarity, we'll use a custom approach
            faiss_index = None
            print(f"Prepared {len(candidate_features_np)} candidate embeddings for angle similarity")
        else:
            # Use FAISS IndexFlatIP for cosine similarity
            faiss_index = faiss.IndexFlatIP(dim)
            faiss_index.add(candidate_features_np)
            print(f"Built FAISS index with {faiss_index.ntotal} candidates")
        
        # Clear GPU memory
        del candidate_features_list
        torch.cuda.empty_cache()
    else:
        faiss_index = None
        candidate_features = None
        candidate_features_np = None
    
    # Phase 2: Process query embeddings in batches and search
    print("Phase 2: Processing query embeddings and searching...")
    query_features = []
    query_ids = []
    
    with torch.no_grad():
        query_dataloader = accelerator.prepare(query_dataloader)
        
        for batch in tqdm(query_dataloader, disable=not is_main_process, desc="Processing queries"):
            batch = tensors_to_device(batch, device)
            query_embed, batch_query_ids, _ = model(**batch, inference=True)
            query_embed = F.normalize(query_embed, dim=-1)
            query_embed = accelerator.gather_for_metrics(query_embed)
            batch_query_ids = accelerate.utils.gather_object(batch_query_ids)[:len(query_embed)]
            query_ids.extend(batch_query_ids)
            query_features.append(query_embed)
    
    query_features = torch.cat(query_features, dim=0)
    
    # Phase 3: Perform similarity search
    if is_main_process:
        print("Phase 3: Performing similarity search...")
        
        query_features_np = query_features.float().cpu().numpy()
        # Use FAISS for cosine similarity
        print("Using FAISS for cosine similarity...")
        k = 50
        batch_size = 1024
        index = []
        scores = []
        
        for i in tqdm(range(0, len(query_features_np), batch_size), desc="FAISS search"):
            batch_query = query_features_np[i:i+batch_size]
            batch_scores, batch_indices = faiss_index.search(batch_query, k)
            scores.extend(batch_scores.tolist())
            index.extend(batch_indices.tolist())
    
        # Convert results
        cand_names = np.array([[unhash_did(candidate_ids[item]) for item in row] for row in index])
        query_names = [unhash_qid(item) for item in query_ids]


        # Save results
        save_dir_name = "./LamRA_Ret_eval_results"
        if not os.path.exists(save_dir_name):
            os.makedirs(save_dir_name)
        save_name = args.qrels_path.split('/')[-1].replace('_qrels.txt', '')
        model_name = args.model_id.split('/')[-1]
        save_name = f"{save_name}_{model_name}_faiss"
        
        # with open(f"{save_dir_name}/{save_name}_query_names.json", 'w') as f:
        #     json.dump(query_names, f, indent=2)
        # with open(f"{save_dir_name}/{save_name}_cand_names.json", 'w') as f:
        #     json.dump(cand_names.tolist(), f, indent=2)
        # with open(f"{save_dir_name}/{save_name}_scores.json", 'w') as f:
        #     json.dump(scores, f, indent=2)
        
        # Evaluate
        qrel, qid_to_taskid = load_qrel(args.qrels_path)
        
        k_lists = [1, 5, 10, 50]
        res = {}
        
        for k in k_lists:
            res[f'recall_{k}'] = []
        
        for ind, query_name in enumerate(tqdm(query_names, desc="Evaluating")):
            relevant_docs = qrel[query_name]
            retrieved_indices_for_qid = cand_names[ind]
            for k in k_lists:
                recall_at_k = compute_recall_at_k(relevant_docs, retrieved_indices_for_qid, k)
                res[f'recall_{k}'].append(recall_at_k)
        
        for k in k_lists:
            print(f"recall_at_{k} = {sum(res[f'recall_{k}']) / len(res[f'recall_{k}'])}")
        
        model_name = args.model_id.split('/')[-1]
        with open(f"{save_dir_name}/{model_name}_faiss_results.txt", 'a') as f:
            f.write(args.qrels_path + '\n')
            for k in k_lists:
                f.write(f"recall_at_{k} = {sum(res[f'recall_{k}']) / len(res[f'recall_{k}'])}" + '\n')
        print(f"Write output to {save_dir_name}/{model_name}_faiss_results.txt")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--query_data_path', type=str)
    parser.add_argument('--cand_pool_path', type=str)
    parser.add_argument('--instructions_path', type=str)
    parser.add_argument('--qrels_path', type=str)
    parser.add_argument('--batch_size', type=int, default=8)
    parser.add_argument('--model_max_length', type=int, default=1024)
    parser.add_argument('--original_model_id', type=str)
    parser.add_argument('--model_id', type=str)
    parser.add_argument('--query_cand_pool_path', type=str)
    parser.add_argument('--image_path_prefix', type=str)
    parser.add_argument('--use_angle_sim', type=bool, default=False)
    parser.add_argument('--nocausal_attn', type=bool, default=False)

    args = parser.parse_args()
    eval_faiss(args)
